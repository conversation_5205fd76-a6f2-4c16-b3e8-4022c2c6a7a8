import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import InterestedCreatorCreatorTypePagePropsController from "./controllers/InterestedCreatorCreatorTypePagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import { interestedCreatorPages } from "pages/interested-creators/information";

const interestedCreatorCreatorTypesProps = (locale: string) =>
  serverPropsControllerFactory(
    new InterestedCreatorCreatorTypePagePropsController(
      ApiContainer.get("options"),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient),
      interestedCreatorPages.creatorTypes
    )
  );

export default interestedCreatorCreatorTypesProps;

import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { CompletePageProps } from "pages/interested-creators/complete";
import { interestedCreatorPages } from "pages/interested-creators/information";
import featureFlags from "utils/feature-flags";

export default class ApplicationCompletePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<CompletePageProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient,
    private readonly page?: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<CompletePageProps>> {
    const nucleusId = this.identity(req).nucleusId;
    const interestedCreatorFromSession = this.hasSession(req, `${this.program}.interestedCreator`)
      ? (this.session(req, `${this.program}.interestedCreator`) as InterestedCreator)
      : null;
    const interestedCreator = await this.getInterestedCreator(nucleusId, interestedCreatorFromSession);
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        locale: this.currentLocale,
        interestedCreator,
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "complete"]))
      }
    };
  }

  private async getInterestedCreator(
    _nucleusId: number,
    interestedCreator: InterestedCreator
  ): Promise<InterestedCreator | null> {
    if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      return interestedCreator;
    }

    let existingApplicantInformation;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplicantInformation = await this.applications.forCreatorWithProgram(
        interestedCreator?.nucleusId,
        config.PROGRAM_CODE
      );
    } else {
      existingApplicantInformation = await this.applications.forCreatorWithApplicationStatus(
        interestedCreator?.nucleusId
      );
    }
    if (!existingApplicantInformation) {
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.information) {
      interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.creatorTypes) {
      if (
        (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
        existingApplicantInformation
      ) {
        interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation?.creatorTypes;
      }
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.franchises) {
      interestedCreator = {
        ...interestedCreator,
        preferredFranchises: [
          ...interestedCreator.preferredFranchises,
          ...(existingApplicantInformation.applicantInformation?.preferredFranchises ?? [])
        ]
      };
      return interestedCreator;
    }

    interestedCreator = {
      ...interestedCreator,
      createdDate: existingApplicantInformation.createdDateformattedWithoutTime(this.currentLocale)
    };

    return interestedCreator;
  }
}

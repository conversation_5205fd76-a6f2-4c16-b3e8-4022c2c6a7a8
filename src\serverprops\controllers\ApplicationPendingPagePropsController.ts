import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ApplicationPendingProps } from "pages/interested-creators/application-pending";
import featureFlags from "utils/feature-flags";

export default class ApplicationPendingPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ApplicationPendingProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<ApplicationPendingProps>> {
    const nucleusId = this.identity(req).nucleusId;
    const application = await this.getApplication(nucleusId);
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !application?.isPending()) return { notFound: true };

    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : false;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        application: Object.assign({}, application),
        locale: this.currentLocale,
        showInitialMessage,
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "application-pending"])),
        RE_APPLY_THRESHOLD_IN_DAYS: +config.RE_APPLY_THRESHOLD_IN_DAYS
      }
    };
  }

  private async getApplication(nucleusId: number): Promise<InterestedCreatorApplicationStatus> {
    let existingApplication;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplication = await this.applications.forCreatorWithProgram(
        nucleusId as unknown as number,
        config.PROGRAM_CODE
      );
    } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      existingApplication = await this.applications.forCreatorWithApplicationStatus(nucleusId as unknown as number);
    } else {
      existingApplication = await this.applications.forCreatorWith(nucleusId as unknown as number);
    }
    if (existingApplication) {
      existingApplication.createdDate = this.currentLocale
        ? existingApplication.createdDateformattedWithoutTime(this.currentLocale)
        : existingApplication.createdDate;

      if (existingApplication.canResubmitRequestDate) {
        existingApplication.canResubmitRequestDate = existingApplication.formatResubmitRequestDateWithoutTime(
          this.currentLocale
        );
      }
    }

    return existingApplication;
  }
}

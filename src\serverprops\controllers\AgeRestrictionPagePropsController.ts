import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { AgeRestrictionProps } from "pages/interested-creators/age-restriction";

export default class AgeRestrictionPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<AgeRestrictionProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<AgeRestrictionProps>> {
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "age-restriction"]))
      }
    };
  }
}

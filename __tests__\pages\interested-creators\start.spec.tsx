import React from "react";
import Start from "../../../pages/interested-creators/start";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../../helpers/page";
import { useRouter } from "next/router";
import { screen } from "@testing-library/react";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context/DependencyContext");

describe("Interested Creator Start Page", () => {
  const router = { locale: "en-us", push: jest.fn() };
  const analytics = {};
  const startProps = {};

  beforeEach(() => {
    jest.useFakeTimers();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({ analytics });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllTimers(); // Clear timers after each test
  });

  it("should redirect to home page when user click on close icon", async () => {
    renderPage(<Start {...startProps} />);
    jest.runAllTimersAsync();

    // Click on page close icon
    await userEvent.click(screen.getByTestId("interested-creator-header-close-icon"));

    // Expect the page to have been redirected to the home page
    expect(router.push).toHaveBeenCalledTimes(1);
    expect(router.push).toHaveBeenCalledWith("/");
  });

  it("shows tab title as 'Start your submission'", async () => {
    renderPage(<Start {...startProps} />);
    jest.runAllTimers();

    expect(document.title).toMatch(/information:interestedCreatorTitle/);
  });
});

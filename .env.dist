# Documentation
TAG=latest
# EADP/Origin Login
LOGIN_URL=https://accounts.int.ea.com/connect/auth?client_id=CNEAIT_DEMO_SERVER_WEB_APP&response_type=code&redirect_uri=http://localhost:3000/api/authenticate
LOGOUT_URL=https://accounts.int.ea.com/connect/logout?client_id=CNEAIT_DEMO_SERVER_WEB_APP&redirect_uri=http://localhost:3000/api/close-session
CREATE_ACCOUNT_URL=https://accounts.int.ea.com/connect/auth?response_type=code&client_id=CNEAIT_DEMO_SERVER_WEB_APP&display=junoWeb/create&redirect_uri=http://localhost:3000/api/authenticate
CLIENT_ID=CNEAIT_DEMO_SERVER_WEB_APP
LOGIN_REDIRECT_URI=http://localhost:3000/api/authenticate
APP_ENV=local
APP_DEBUG=true
OPERATIONS_API_BASE_URL=https://dev-intrnl-services.cn.ea.com/cn-operations-api
OPPORTUNITIES_API_BASE_URL=https://dev-services.cn.ea.com/cn-opportunities-api
FLAG_OPPORTUNITIES_API_CLIENT=true
CONTENT_SCANNING_API_BASE_URL=https://dev-services.cn.ea.com/cn-content-scanning-api
CONTENT_SUBMISSION_BASE_URL=https://dev-services.cn.ea.com/cn-content-submission-api
FLAG_SIGNED_URL_V1_ENABLED=false
FLAG_SIGNED_URL_V2_ENABLED=true
METADATA_API_BASE_URL=https://localhost:3010/cn-metadata-api
CREATORS_API_BASE_URL=https://dev-frontends.cn.ea.com/cn-creators-api
PAYMENTS_API_BASE_URL=https://dev-services.cn.ea.com/cn-payments-api
LEGAL_API_BASE_URL=https://dev-services.cn.ea.com/cn-legal-api
COMMUNICATIONS_API_BASE_URL=https://dev-services.cn.ea.com/cn-communications-api
ACCESS_TOKEN_BASE_URL=https://dev-services.cn.ea.com/security
CONTENT_MANAGEMENT_API_BASE_URL=https://dev-services.cn.ea.com/cn-content-management-api
API_CLIENT_ID=
API_CLIENT_SECRET=
# Session
COOKIE_PASSWORD=
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax
COOKIE_SECURE=false
COOKIE_DOMAIN=
SESSION_COOKIE_NAME=
SESSION_PROXY=false
SESSION_TTL=14400
# Google Analytics
GTM_AUTH=
GTM_PREVIEW=
# Connected Accounts OAuth
YOUTUBE_CLIENT_ID=
YOUTUBE_CLIENT_SECRET=
YOUTUBE_CLIENT_REDIRECT_URI=http://localhost:3000/api/youtube-connect
YOUTUBE_SCOPES=profile,https://www.googleapis.com/auth/youtube.readonly,https://www.googleapis.com/auth/yt-analytics.readonly
TWITCH_CLIENT_ID=
TWITCH_CLIENT_REDIRECT_URI=http://localhost:3000/api/twitch-connect
TWITCH_SCOPES=user_read+channel_read
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_REDIRECT_URI=http://localhost:3000/api/facebook-connect
FACEBOOK_STATE=user_read+channel_read
FACEBOOK_SCOPES=email,public_profile,read_insights,pages_show_list,pages_read_engagement,business_management
FACEBOOK_API_VERSION=v18.0
INSTAGRAM_CLIENT_ID=
INSTAGRAM_CLIENT_REDIRECT_URI=http://localhost:3000/api/instagram-connect
INSTAGRAM_STATE=d5fb3181be01ccd936bce45a776836c6
INSTAGRAM_SCOPE=email,public_profile,read_insights,pages_show_list,instagram_basic,instagram_manage_insights,business_management
INSTAGRAM_API_VERSION=v18.0
TIKTOK_CLIENT_ID=
TIKTOK_CLIENT_REDIRECT_URI=http://localhost:3000/api/tiktok-connect
TIKTOK_SCOPES=user.info.profile,user.info.stats,video.list
DISCORD_CLIENT_ID=
DISCORD_CLIENT_REDIRECT_URI=http://localhost:3000/api/discord-connect
DISCORD_SCOPES='email guilds guilds.join gdm.join identify'
WATERMARKS_URL=https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/watermark/v1/EA+CreatorNetwork+Logoset.zip
DEFAULT_FRANCHISE_IMAGE=https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/franchises/ea-no-franchise.png
PACTSAFE_ID=
CACHE_PREFIX=web_dev_
REDIS_PORT=6379
REDIS_HOST=localhost
REDIS_SCALE_READ=slave
DEBUG=true
LOG_LEVEL=INFO
FALLBACK_CREATOR_TYPES=[{"youtuber":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/YOUTUBER.png"},{"live_streamer":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIVE_STREAMER.png"},{"photographer":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PHOTOGRAPHER.png"},{"designer_artist":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/DESIGNER_ARTIST.png"},{"blogger":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/BLOGGER.png"},{"podcaster":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PODCASTER.png"},{"lifestyle":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIFESTYLE.png"},{"cosplayer":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/COSPLAYER.png"},{"animator":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/ANIMATOR.png"},{"screenshoter":"https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/SCREENSHOTER.png"}]
TERMS_STATUS_CACHE_TTL=86400
INTERESTED_CREATOR=true
AMPLITUDE_API_KEY=
AMPLITUDE_ENV=
YOUTUBE_HOSTS=["youtube.com","m.youtube.com"]
TWITCH_HOSTS=["twitch.tv"]
INSTAGRAM_HOSTS=["instagram.com"]
FACEBOOK_HOSTS=["facebook.com"]
TIKTOK_HOSTS=["tiktok.com"]
CN_LAUNCH_DATE="2021-09-01"
BUILD_VERSION=LOCAL
SUPPORTED_LOCALES=["en-us","ja-jp","de-de","fr-fr","es-es","it-it"]
SENTRY_DSN=
HTTP_REQUEST_TIMEOUT=15000
TIKTOK_AUTH_BASE_URI=https://www.tiktok.com/v2/auth/authorize
UPDATE_OPPORTUNITY_DETAILS=true
INTERESTED_CREATOR_REAPPLY_PERIOD=true
RE_APPLY_THRESHOLD_IN_DAYS=90
FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED=true
FLAG_INITIAL_MESSAGE=true
INITIAL_MESSAGE_TITLE=
INITIAL_MESSAGE_DESCRIPTION=
FLAG_OBSERVABILITY=false
OTLP_TRACE_EXPORTER_URL=http://localhost:4317/v1/traces
ARTIFACTORY=docker.artifactory.ea.com
ANALYTICS_SAMPLE_RATE=1
FLAG_COUNTRIES_BY_TYPE=true
NOTIFICATIONS_MFE_BASE_URL=http://localhost:3001/cn-notifications-mfe
NOTIFICATIONS_MFE_NAME=cn-notifications-mfe
SERVICE_NAME=cn-creator-hub
FLAG_NEW_NAVIGATION_ENABLED=false
FLAG_NEW_FOOTER_ENABLED=false
PROGRAM_CODE=creator_network
FLAG_COMMUNICATIONS_API_CLIENT=true
FLAG_ONBOARDING_CUSTOM_LINKS=true
MICROCOPY_NOTIFICATIONS_PAGE=["MICROCOPY: NotificationsPage","MICROCOPY: Programs","MICROCOPY: SocialAccounts"]
MICROCOPY_NO_ACCOUNT_PAGE=["MICROCOPY: NoAccount"]
FLAG_CREATORS_API_WITH_PROGRAM=true
MENUITEM_NAMES='{"sims_creator_program":{"label":"UFX","gradients":["#2E900A","#6FF049"]},"affiliate":{"label":"SupportACreator","gradients":["#6A30D3","#A133DD"]},"creator_network":{"label":"CreatorNetwork","gradients":["#2D2EFE","#4CCEF7"]}}'
NOTIFICATION_BASE_URLS='{"creator_network":"https://dev-creatornetwork.ea.com","affiliate":"https://dev-www.ea.com/support-a-creator","sims_creator_program":"https://dev-simsugx.ea.com"}'
SINGLE_PROGRAM_NOTIFICATIONS=true
DEFAULT_NOTIFICATION_PROGRAM=creator_network
FLAG_CONTENT_WITH_FINAL_REMARK=true
SEARCH_CREATORS_API_WITH_PROGRAM=true
FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM=true
FLAG_SUBMITTED_CONTENT_WITH_PROGRAM=true
FLAG_PER_PROGRAM_PROFILE=true
FLAG_OPPORTUNITIES_PER_PROGRAM=true
FLAG_SEND_EMAIL_WITH_PROGRAM=true
DEFAULT_AVATAR_IMAGE=https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png

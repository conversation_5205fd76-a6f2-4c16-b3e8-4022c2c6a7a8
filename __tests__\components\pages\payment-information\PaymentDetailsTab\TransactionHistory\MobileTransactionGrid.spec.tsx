import React from "react";
import { act, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { mockMatchMedia } from "../../../../../helpers/window";
import {
  mapOpportunityTypeForAPI,
  PaymentsHistory,
  PaymentsHistoryWithCreatorCode
} from "../../../../../../src/api/services/PaymentsService";
import { paymentHistoryGridTranslations } from "../../../../../translations/payment-information";
import MobileTransactionGrid from "@components/pages/payment-information/PaymentDetailsTab/TransactionHistory/MobileTransactionGrid";
import { aPaymentsHistory } from "../../../../../factories/paymentsHistory/PaymentsHistory";
import { aTransaction } from "../../../../../factories/paymentsHistory/Transaction";
import { aPaymentsHistoryWithCreatorCode } from "../../../../../factories/paymentsHistory/PaymentsHistoryWithCreatorCode";
import { aTransactionWithCreatorCode } from "../../../../../factories/paymentsHistory/TransactionWithCreatorCode";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";

describe("MobileTransactionGrid", () => {
  mockMatchMedia();

  const paginationAndLabelProps = {
    labels: paymentHistoryGridTranslations,
    isShowingPagination: true,
    paginationProps: {
      next: "Next",
      prev: "Prev",
      pages: [1],
      currentPage: 1,
      onPageChange: jest.fn()
    },
    analytics: {} as unknown as BrowserAnalytics
  };
  const paymentsHistory = new PaymentsHistory(aPaymentsHistory());
  const transactionGridProps = {
    paymentsHistory,
    ...paginationAndLabelProps
  };

  it("is accessible", async () => {
    const { container } = render(<MobileTransactionGrid {...transactionGridProps} />);
    let results;

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("has link to download contract", () => {
    render(<MobileTransactionGrid {...transactionGridProps} />);

    expect(screen.getByRole("button", { name: "Download Contract" })).toHaveAttribute(
      "href",
      paymentsHistory.details[0].contractLink
    );
  });

  it("logs 'Downloaded Payment Contract' event on click of link to download contract", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const analytics = { downloadedPaymentContract: jest.fn() } as unknown as BrowserAnalytics;
    render(<MobileTransactionGrid {...transactionGridProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: "Download Contract" }));

    expect(analytics.downloadedPaymentContract).toHaveBeenCalledTimes(1);
    expect(analytics.downloadedPaymentContract).toHaveBeenCalledWith({ locale: "en-us" });
  });

  it("logs 'Clicked Opportunity Description' event on click of opportunity link", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us", push: jest.fn().mockResolvedValue(true) }));
    const analytics = { clickedOpportunityDescription: jest.fn() } as unknown as BrowserAnalytics;
    render(<MobileTransactionGrid {...transactionGridProps} analytics={analytics} />);

    await userEvent.click(screen.queryByText(`${paymentsHistory.details[0].opportunityTitle.slice(0, 20)}...`));

    expect(analytics.clickedOpportunityDescription).toHaveBeenCalledTimes(1);
    expect(analytics.clickedOpportunityDescription).toHaveBeenCalledWith({ locale: "en-us" });
  });

  it("displays only 20 characters with an ellipsis if opportunity title has more than 20 characters", () => {
    render(<MobileTransactionGrid {...transactionGridProps} />);

    expect(screen.queryByText(`${paymentsHistory.details[0].opportunityTitle.slice(0, 20)}...`)).toBeInTheDocument();
  });

  it("displays tooltip with date title", () => {
    const { container } = render(<MobileTransactionGrid {...transactionGridProps} />);

    // TODO: this should be reachable either via role/label or test ID: https://jaas.ea.com/browse/CRNE-7440
    // eslint-disable-next-line
    expect(container.querySelector(".transaction-history-grid-header-date-icon .tooltip-element")).toBeInTheDocument();
  });

  it("displays '-' as processed date if transaction status is 'pending'", () => {
    const paymentsHistory = new PaymentsHistory(
      aPaymentsHistory({ details: [aTransaction({ status: "PENDING", opportunityType: "marketing_opportunity" })] })
    );

    render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);

    expect(screen.queryByText("-")).toBeInTheDocument();
  });

  it("displays its contents", () => {
    const paymentsHistory = new PaymentsHistory(
      aPaymentsHistory({
        details: [
          aTransaction({
            opportunityTitle: "Fifa 2022",
            opportunityType: "marketing_opportunity",
            status: "PAID",
            amount: "21987.00"
          })
        ]
      })
    );

    render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);

    expect(screen.getByRole("img")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Download Contract" })).toBeInTheDocument();
    expect(screen.queryByText(/Fifa 2022/i)).toBeInTheDocument();
    expect(screen.queryByText(/\$21,987/i)).toBeInTheDocument();
    expect(screen.queryByText(/Opportunity/i)).toBeInTheDocument();
    expect(screen.queryByText(/Processed/i)).toBeInTheDocument();
    expect(screen.queryByText(paymentsHistory.details[0].processedDate.format("MM/DD/YY"))).toBeInTheDocument();
  });

  it("displays creator code text and perk icon", () => {
    const paymentsHistory = new PaymentsHistoryWithCreatorCode(
      aPaymentsHistoryWithCreatorCode({
        details: [
          aTransactionWithCreatorCode({
            opportunityType: "support_a_creator"
          })
        ]
      })
    );

    const { container } = render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);

    expect(screen.queryByText(/Creator Code/i)).toBeInTheDocument();
    // TODO: this should be reachable either via role/label or test ID: https://jaas.ea.com/browse/CRNE-7440
    // eslint-disable-next-line
    expect(container.querySelector(".creator-perk")).toBeInTheDocument();
  });

  it("displays creator code on hover over perk icon", async () => {
    const paymentsHistory = new PaymentsHistoryWithCreatorCode(
      aPaymentsHistoryWithCreatorCode({
        details: [
          aTransactionWithCreatorCode({
            opportunityType: "support_a_creator",
            creatorCode: {
              code: "TESTCREATORCODE1P8"
            }
          })
        ]
      })
    );
    const { container } = render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);
    // TODO: this should be reachable either via role/label or test ID: https://jaas.ea.com/browse/CRNE-7440
    // eslint-disable-next-line
    const creatorCodeTooltip = container.querySelector(".tooltip-element");
    expect(creatorCodeTooltip).toBeInTheDocument();

    await userEvent.hover(creatorCodeTooltip);

    expect(screen.queryByText(/TESTCREATORCODE1P8/i)).toBeInTheDocument();
  });

  it("does not display link and underline for opportunity title when opportunity type is sims_ugx_opportunity", () => {
    const paymentsHistory = new PaymentsHistory(
      aPaymentsHistory({
        details: [
          aTransaction({
            opportunityTitle: "UGX Test Opportunity",
            opportunityType: mapOpportunityTypeForAPI("sims_ugx_opportunity"),
            status: "PAID",
            amount: "1000.00"
          })
        ]
      })
    );

    render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);

    const opportunityTitle = screen.getByText("UGX Test Opportunity");
    expect(opportunityTitle).toBeInTheDocument();
    expect(screen.queryByRole("link", { name: "UGX Test Opportunity" })).not.toBeInTheDocument();
    expect(opportunityTitle).toHaveClass("record-opportunity-title");
  });

  it("displays 'Sims Maker Program' in opportunity type column when opportunity type is sims_ugx_opportunity", () => {
    const paymentsHistory = new PaymentsHistory(
      aPaymentsHistory({
        details: [
          aTransaction({
            opportunityTitle: "UGX Test Opportunity",
            opportunityType: mapOpportunityTypeForAPI("sims_ugx_opportunity"),
            status: "PAID",
            amount: "1000.00"
          })
        ]
      })
    );

    render(<MobileTransactionGrid {...{ paymentsHistory, ...paginationAndLabelProps }} />);

    expect(screen.getByText(paymentHistoryGridTranslations.simsMakerProgram)).toBeInTheDocument();
  });
});

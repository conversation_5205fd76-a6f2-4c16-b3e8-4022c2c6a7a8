import { NoAccountPageMapper } from "@src/contentManagement/NoAccountPageMapper";

describe("NoAccountPageMapper", () => {
  const microCopies = {
    "noAccount.title": "Join Our Creator Network",
    "noAccount.applyNow": "Apply Now",
    "noAccount.exploreTitle": "Explore Opportunities",
    "noAccount.creatorNetwork": "Creator Network",
    "noAccount.availablePerks": "Available Perks",
    "noAccount.subTitlePart1": "Become a Part",
    "noAccount.subTitlePart2": "Of Our Community",
    "noAccount.descriptionPara1": "First paragraph description",
    "noAccount.descriptionPara2": "Second paragraph description",
    "noAccount.descriptionPara3": "Third paragraph description",
    "noAccount.howItWorks": "How It Works"
  };

  it("maps no account page labels", () => {
    const mapper = new NoAccountPageMapper();
    const labels = mapper.map(microCopies).noAccountLabels;

    expect(labels.title).toEqual("Join Our Creator Network");
    expect(labels.applyNow).toEqual("Apply Now");
    expect(labels.exploreTitle).toEqual("Explore Opportunities");
    expect(labels.creatorNetwork).toEqual("Creator Network");
    expect(labels.availablePerks).toEqual("Available Perks");
    expect(labels.subTitlePart1).toEqual("Become a Part");
    expect(labels.subTitlePart2).toEqual("Of Our Community");
    expect(labels.descriptionPara1).toEqual("First paragraph description");
    expect(labels.descriptionPara2).toEqual("Second paragraph description");
    expect(labels.descriptionPara3).toEqual("Third paragraph description");
    expect(labels.howItWorks).toEqual("How It Works");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new NoAccountPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key noAccount.title is absent");
  });
});

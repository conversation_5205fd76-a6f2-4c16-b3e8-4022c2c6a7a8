{"scripts": {"dev": "cross-env NEXT_PRIVATE_LOCAL_WEBPACK=true DEBUG=express-session next dev", "debug": "cross-env NODE_OPTIONS='--inspect' next dev", "build": "NEXT_PRIVATE_LOCAL_WEBPACK=true NODE_OPTIONS='--max-old-space-size=9216' next build --no-lint", "start": "NEXT_PRIVATE_LOCAL_WEBPACK=true next start", "lint": "next lint", "lint:tests": "eslint __tests__", "test": "jest --runInBand=true --cache --forceExit", "test:types": "tsc --noEmit", "test-watch": "jest --watch", "test:coverage": "jest --runInBand=true --coverage --cache", "format:check": "prettier . --check", "format": "prettier . --write", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "prepare": "husky install", "lint-staged": "lint-staged", "generate:api": "tsx .plop/index.ts"}, "dependencies": {"@amplitude/analytics-browser": "2.9.3", "@amplitude/identify": "1.10.2", "@amplitude/node": "1.10.2", "@amplitude/plugin-session-replay-browser": "1.6.23", "@eait-playerexp-cn/activity-feed": "2.0.0", "@eait-playerexp-cn/activity-logger": "1.1.0", "@eait-playerexp-cn/api-problem": "1.1.0", "@eait-playerexp-cn/authentication": "3.2.1", "@eait-playerexp-cn/client-kernel": "1.0.0", "@eait-playerexp-cn/core-ui-kit": "8.26.7", "@eait-playerexp-cn/creator-types": "1.3.0", "@eait-playerexp-cn/creators-http-client": "2.0.1", "@eait-playerexp-cn/http": "1.0.0", "@eait-playerexp-cn/http-client": "1.4.3", "@eait-playerexp-cn/identity": "3.0.0", "@eait-playerexp-cn/identity-types": "1.5.1", "@eait-playerexp-cn/interested-creators-authentication-plugins": "2.1.0", "@eait-playerexp-cn/metadata-http-client": "1.0.0", "@eait-playerexp-cn/metadata-types": "1.0.1", "@eait-playerexp-cn/object-mapper": "1.0.0", "@eait-playerexp-cn/onboarding-authentication-plugins": "3.2.2", "@eait-playerexp-cn/server-kernel": "7.2.0", "@eait-playerexp-cn/telemetry": "1.0.0", "@headlessui/react": "^1.2.0", "@module-federation/nextjs-mf": "8.6.0", "@opentelemetry/auto-instrumentations-web": "0.40.0", "@opentelemetry/context-zone": "1.25.1", "@opentelemetry/core": "1.26.0", "@opentelemetry/exporter-trace-otlp-http": "0.53.0", "@opentelemetry/resources": "1.26.0", "@opentelemetry/sdk-node": "0.53.0", "@opentelemetry/sdk-trace-node": "1.26.0", "@opentelemetry/sdk-trace-web": "1.25.1", "@opentelemetry/semantic-conventions": "1.26.0", "@sentry/nextjs": "7.119.2", "@types/lodash": "4.14.74", "camelcase": "^6.2.0", "classnames": "^2.2.6", "cookie": "1.0.2", "email-validator": "^2.0.4", "file-type": "20.1.0", "formidable": "3.5.2", "google-auth-library": "^7.0.4", "googleapis": "^73.0.0", "i18next": "21.10.0", "next": "14.2.25", "next-connect": "1.0.0", "next-i18next": "9.2.0", "rc-tooltip": "^5.2.2", "react": "18.2.0", "react-datepicker": "4.25.0", "react-dom": "18.2.0", "react-hook-form": "7.54.2", "react-i18next": "11.18.6", "react-scroll": "^1.8.9", "react-toastify": "^7.0.4", "reflect-metadata": "^0.1.13", "sanitize-html": "^2.4.0", "typedi": "0.10.0", "uuid": "9.0.1"}, "devDependencies": {"@amplitude/types": "1.10.2", "@babel/core": "^7.13.8", "@babel/plugin-proposal-decorators": "7.19.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@commitlint/cli": "19.2.2", "@commitlint/config-conventional": "19.2.2", "@eait-playerexp-cn/identity-test-fixtures": "1.1.0", "@eait-playerexp-cn/interested-creators-ui": "1.7.0", "@eait-playerexp-cn/metadata-test-fixtures": "1.0.1", "@eait-playerexp-cn/notifications-ui": "2.8.2", "@eait-playerexp-cn/creator-test-fixtures": "1.6.0", "@eait-playerexp-cn/onboarding-ui": "1.9.1", "@faker-js/faker": "9.6.0", "@storybook/addon-essentials": "7.6.8", "@storybook/addon-interactions": "7.6.8", "@storybook/addon-links": "7.6.8", "@storybook/addon-styling-webpack": "0.0.6", "@storybook/blocks": "7.6.8", "@storybook/builder-webpack5": "7.6.8", "@storybook/nextjs": "7.6.8", "@storybook/react": "7.6.8", "@storybook/test": "7.6.8", "@tailwindcss/aspect-ratio": "0.4.0", "@tailwindcss/line-clamp": "0.3.1", "@tailwindcss/typography": "0.5.4", "@testing-library/jest-dom": "6.1.2", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.1", "@types/express": "5.0.0", "@types/express-session": "1.18.0", "@types/jest": "29.5.4", "@types/jest-axe": "^3.5.3", "@types/minimist": "^1.2.2", "@types/react": "18.0.28", "@typescript-eslint/eslint-plugin": "8.2.0", "autoprefixer": "10.4.5", "axios-mock-adapter": "1.21.1", "babel-loader": "^8.2.2", "babel-plugin-inline-react-svg": "2.0.2", "babel-plugin-transform-typescript-metadata": "0.3.2", "chalk-pipe": "^5.1.1", "cross-env": "7.0.3", "cssnano": "5.1.15", "eslint": "8.57.0", "eslint-config-next": "14.2.6", "eslint-plugin-testing-library": "6.3.0", "fishery": "2.2.2", "husky": "^7.0.0", "identity-obj-proxy": "^3.0.0", "jest": "29.7.0", "jest-axe": "8.0.0", "jest-environment-jsdom": "29.6.4", "jest-html-reporters": "3.1.7", "lint-staged": "^12.3.2", "minimist": "^1.2.6", "node-mocks-http": "^1.10.1", "plop": "^3.1.1", "postcss": "8.4.31", "postcss-import": "^14.0.0", "prettier": "2.5.1", "prettier-plugin-tailwindcss": "0.1.10", "storybook": "7.6.20", "style-loader": "3.3.4", "tailwindcss": "3.4.4", "tsc-files": "1.1.4", "tsx": "4.17.0", "typescript": "5.5.4"}, "lint-staged": {"*.{ts,tsx}": ["tsc-files --noEmit --pretty", "eslint --quiet --cache --fix", "prettier --write --parser typescript", "jest --bail --findRelatedTests --passWithNoTests"], "*.{js,jsx}": ["eslint --quiet --cache --fix", "prettier --write", "jest --bail --findRelatedTests --passWithNoTests"], "*.json": ["prettier --write"], "*.css": ["prettier --write --parser css"]}}
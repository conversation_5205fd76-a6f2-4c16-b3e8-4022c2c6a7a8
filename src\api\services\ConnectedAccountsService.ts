import { FacebookPage } from "@src/accounts/ConnectedAccountsHttpClient";
import ConnectedAccount from "@src/channels/ConnectedAccount";
import client from "./Client";

type Pages = {
  pages: Array<FacebookPage>;
};

export type ErrorType = {
  code: string;
  message: string;
};

type SelectedPage = {
  pageId: string;
  pageAccessToken: string;
};

const clearAccountType = async (): Promise<void> => {
  await client.delete("/api/account-types");
};

const clearFbPages = async (): Promise<void> => {
  await client.delete("/api/facebook-connect");
};

const removeDiscordAccount = async (id: string): Promise<void> => {
  await client.delete(`/api/discord-accounts/${id}`);
};

const connectFbPages = async (selectedPage: SelectedPage): Promise<void> => {
  await client.post("/api/facebook-channels", { body: selectedPage });
};

const getConnectedAccounts = async (nucleusId: number): Promise<Array<ConnectedAccount>> => {
  const response = await client.get("/api/connected-accounts", { query: { nucleusId } });
  return response.data;
};

const getAllConnectedAccountsWithExpirationStatus = async (nucleusId: number): Promise<Array<ConnectedAccount>> => {
  const response = await client.get("/api/v2/connected-accounts", { query: { nucleusId } });
  return response.data;
};

const getFacebookPages = async (): Promise<Pages> => {
  const response = await client.get("/api/facebook-pages");
  return response.data;
};

const removeConnectedAccount = async (accountId: string): Promise<void> => {
  await client.delete(`/api/accounts/${accountId}`);
};

const getConnectAccountErrors = async (): Promise<ErrorType> => {
  const response = await client.get("/api/errors");
  return response.data;
};

const deleteConnectedAccountErrors = async (): Promise<ErrorType> => {
  const response = await client.delete("/api/sessions/error");
  return response.data;
};

const AccountsService = {
  clearFbPages,
  connectFbPages,
  clearAccountType,
  getConnectedAccounts,
  getFacebookPages,
  removeConnectedAccount,
  getConnectAccountErrors,
  deleteConnectedAccountErrors,
  getAllConnectedAccountsWithExpirationStatus,
  removeDiscordAccount
};

export default AccountsService;

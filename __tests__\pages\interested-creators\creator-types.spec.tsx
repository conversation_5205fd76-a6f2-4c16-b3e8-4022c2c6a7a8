import "reflect-metadata";
import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import InterestedCreatorsCreatorTypes from "../../../pages/interested-creators/creator-types";
import userEvent from "@testing-library/user-event";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { mockMatchMedia } from "../../helpers/window";
import { anInitialInterestedCreator } from "../../factories/initialInterestedCreators/InitialInterestedCreator";
import { useDependency } from "@src/context/DependencyContext";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));

describe("InterestedCreatorsCreatorTypes", () => {
  const initialInterestedCreator = anInitialInterestedCreator();
  const interestedCreator = {
    nucleusId: 1234567890,
    defaultGamerTag: "",
    originEmail: "",
    dateOfBirth: undefined,
    contentUrls: [{ url: "", followers: "" }],
    creatorTypes: []
  };
  const interestedCreatorsCreatorTypesProps = {
    interestedCreator,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };
  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  mockMatchMedia();
  const analytics = {
    cancelledCreatorApplication: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ analytics, metadataClient: {} });
    (MetadataService as jest.Mock).mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue([aCreatorType({ label: "YouTuber" })])
    });
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
  });

  it("shows modal with link to logout", async () => {
    render(<InterestedCreatorsCreatorTypes {...interestedCreatorsCreatorTypesProps} />);
    await screen.findByText(/YouTuber/i);

    // Click on page close icon
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    // Verify elements in modal are shown
    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("shows modal when cancel  button is clicked", async () => {
    render(<InterestedCreatorsCreatorTypes {...interestedCreatorsCreatorTypesProps} />);
    expect(await screen.findByText(/YouTuber/i)).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("closes modal when no button is clicked on modal", async () => {
    render(<InterestedCreatorsCreatorTypes {...interestedCreatorsCreatorTypesProps} />);
    expect(await screen.findByText(/YouTuber/i)).toBeInTheDocument();
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    await userEvent.click(await screen.findByRole("button", { name: "no" })); // Clicking "No" on modal will close it

    expect(screen.queryByRole("button", { name: /Close$/i })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "no" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: /yes/i })).not.toBeInTheDocument();
    expect(screen.queryByText(/common:modalConfirmationTitle/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc1/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc2/i)).not.toBeInTheDocument();
  });

  it("redirect to logout when yes is clicked on modal", async () => {
    render(<InterestedCreatorsCreatorTypes {...interestedCreatorsCreatorTypesProps} />);
    expect(await screen.findByText(/YouTuber/i)).toBeInTheDocument();
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i })); // Clicking "Yes" on modal will log out

    await waitFor(() => {
      // Expect the page to have been redirected to the logout page
      expect(router.push).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith("/api/logout");
      // Expect the Cancel event to be logged
      expect(analytics.cancelledCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.cancelledCreatorApplication).toHaveBeenCalledWith({ locale: "en-us", page: "/" });
    });
  });

  it("navigates to 'Information' page when user taps on back button", async () => {
    render(<InterestedCreatorsCreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    expect(router.push).toHaveBeenCalledWith("/interested-creators/information");
  });
});

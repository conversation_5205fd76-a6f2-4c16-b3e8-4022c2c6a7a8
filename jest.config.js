const nextJest = require("next/jest");

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: "./"
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],
  testEnvironment: "jsdom",
  bail: 1,
  verbose: true,
  transformIgnorePatterns: ["/node_modules/", "^.+\\.module\\.(css|sass|scss)$"],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/.next/",
    "__tests__/doubles",
    "__tests__/factories",
    "__tests__/helpers",
    "__tests__/translations"
  ],
  collectCoverageFrom: [
    "pages/**/*.{js,jsx,ts,tsx}",
    "components/**/*.{js,jsx,ts,tsx}",
    "src/**/*.{js,jsx,ts,tsx}",
    "utils/*.{js,ts}",
    "!src/ApiContainer.ts",
    "!src/ampli/index.js",
    "!pages/_app.js",
    "!pages/api/**/*.{js,jsx,ts,tsx}",
    "!**/*.d.ts",
    "!**/node_modules/**",
    "config.ts"
  ],
  coveragePathIgnorePatterns: ["/node_modules/"],
  coverageDirectory: "reports/coverage",
  coverageReporters: ["text", "text-summary", "html"],
  coverageThreshold: {
    global: {
      statements: 76,
      branches: 64,
      functions: 73,
      lines: 77
    }
  },
  reporters: [
    "default",
    [
      "jest-html-reporters",
      { inlineSource: true, publicPath: "reports/tests", filename: "report.html", enableMergeData: true }
    ]
  ],
  moduleNameMapper: {
    // Force module uuid to resolve with the CJS entry point, because Jest does not support package.json.exports. See https://github.com/uuidjs/uuid/issues/451
    uuid: require.resolve("uuid")
  }
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);

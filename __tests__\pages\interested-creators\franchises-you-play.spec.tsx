import "reflect-metadata";
import React from "react";
import InterestedCreatorFranchisesYouPlay from "../../../pages/interested-creators/franchises-you-play";
import { screen } from "@testing-library/react";
import { anInterestedCreator } from "../../factories/interestedCreators/InterestedCreator";
import userEvent from "@testing-library/user-event";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { mockMatchMedia } from "../../helpers/window";
import { renderPage } from "../../helpers/page";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { anInitialInterestedCreator } from "../../factories/initialInterestedCreators/InitialInterestedCreator";
import "next/config";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));

describe("InterestedCreatorsFranchisesYouPlay", () => {
  mockMatchMedia();
  const franchisesResponse = [
    { value: "Apex", label: "Apex", image: "" },
    { value: "Fifa", label: "Fifa", image: "" }
  ];
  const interestedCreator = anInterestedCreator({
    nucleusId: 1234567890,
    defaultGamerTag: "121",
    preferredFranchises: [{ id: "Fifa", type: "PRIMARY" }]
  }) as unknown as InterestedCreator;
  const router = { locale: "en-us", push: jest.fn() };
  const initialInterestedCreator = anInitialInterestedCreator();
  const interestedCreatorFranchisesYouPlayProps = {
    interestedCreator,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    INTERESTED_CREATOR_REAPPLY_PERIOD: false
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const analytics = {
    cancelledCreatorApplication: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      metadataClient: {},
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getFranchises: jest.fn().mockResolvedValue(franchisesResponse)
    });
  });

  it("shows modal with link to log out when clicking on the close icon", async () => {
    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);
    // Click on page close icon
    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    // Verify elements in modal are shown
    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("shows modal when cancel  button is clicked", async () => {
    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    // Verify elements in modal are shown
    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("closes modal when 'no'  button is clicked on modal", async () => {
    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);
    // Click Cancel button
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    // Click 'No' on modal
    await userEvent.click(await screen.findByRole("button", { name: "no" }));

    // Verify the modal was closed
    expect(screen.queryByRole("button", { name: /Close$/i })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "no" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: /yes/i })).not.toBeInTheDocument();
    expect(screen.queryByText(/common:modalConfirmationTitle/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc1/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc2/i)).not.toBeInTheDocument();
  });

  it("redirects to log out and logs event when 'yes' is clicked on modal", async () => {
    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);
    // Click on Cancel button
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i })); // take yes on modal to logout

    // Expect the page to have been redirected to the logout page
    expect(router.push).toHaveBeenCalledTimes(1);
    expect(router.push).toHaveBeenCalledWith("/api/logout");
    // Expect the Cancel event to be logged
    expect(analytics.cancelledCreatorApplication).toHaveBeenCalledWith({
      locale: "en-us",
      page: "/"
    });
  });

  it("shows error page when page fails to load", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { exceptionCode: 500 }
    });

    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    await screen.findByText("500");
  });

  it("navigates to 'Creator Type' page when user taps on back button", async () => {
    renderPage(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /Back/i }));

    expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
  });
});

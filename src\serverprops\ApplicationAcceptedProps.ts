import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationAcceptedPagePropsController from "./controllers/ApplicationAcceptedPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";

const applicationAcceptedProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationAcceptedPagePropsController(
      ApiContainer.get("options"),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationAcceptedProps;

import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationCompletePagePropsController from "./controllers/ApplicationCompletePagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";

const applicationCompletePageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationCompletePagePropsController(
      ApiContainer.get("options"),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient),
      null
    )
  );

export default applicationCompletePageProps;

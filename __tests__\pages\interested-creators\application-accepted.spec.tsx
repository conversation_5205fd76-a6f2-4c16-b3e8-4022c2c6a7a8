import React from "react";
import Accepted from "../../../pages/interested-creators/application-accepted";
import { renderPage } from "../../helpers/page";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/context/DependencyContext");

describe("InterestedCreatorsApplicationAccepted", () => {
  const analytics = {
    checkedApplicationStatus: jest.fn()
  };
  const locale = "en-us";

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      configuration: { SUPPORTED_LOCALES: ["en-us"] }
    });
  });

  it("shows tab title as 'Submission Completed'", async () => {
    renderPage(<Accepted locale={locale} />);

    expect(document.title).toMatch(/accepted:pageTitle/);
  });

  it("logs 'Checked Application Status' event", async () => {
    renderPage(<Accepted locale={locale} />);

    expect(analytics.checkedApplicationStatus).toHaveBeenCalledTimes(1);
    expect(analytics.checkedApplicationStatus).toHaveBeenCalledWith({
      locale: "en-us",
      status: "Accepted"
    });
  });
});

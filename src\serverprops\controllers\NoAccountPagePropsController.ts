import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { Identity } from "@eait-playerexp-cn/identity-types";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { NoAccount } from "pages/interested-creators/no-account";
import featureFlags from "utils/feature-flags";

export default class NoAccountPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<NoAccount>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<NoAccount>> {
    const interestedCreator = this.hasSession(req, "noAccountCreator")
      ? (this.session(req, "noAccountCreator") as Identity)
      : null;

    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };

    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator),
        locale: this.currentLocale,
        showInitialMessage,
        ...(await serverSideTranslations(this.currentLocale, ["common", "no-account", "index"]))
      }
    };
  }
}

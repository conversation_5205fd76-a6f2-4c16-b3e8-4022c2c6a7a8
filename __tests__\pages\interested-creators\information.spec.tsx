import "reflect-metadata";
import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import InterestedCreators, {
  Information,
  InterestedCreatorInformationProps
} from "../../../pages/interested-creators/information";
import userEvent from "@testing-library/user-event";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { mockMatchMedia } from "../../helpers/window";
import { anInitialInterestedCreator } from "../../factories/initialInterestedCreators/InitialInterestedCreator";
import { aConnectedAccount } from "../../factories/creators/ConnectedAccounts";
import { renderPage } from "../../helpers/page";
import { onToastClose } from "../../../utils";
import { triggerAnimationEnd } from "../../helpers/toast";
import ConnectedAccountsService from "@src/api/services/ConnectedAccountsService";
import { useDependency } from "@src/context/DependencyContext";
import { aCountry } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../utils", () => ({
  ...(jest.requireActual("../../../utils") as Record<string, unknown>),
  onToastClose: jest.fn()
}));
jest.mock("../../../src/api/services/ConnectedAccountsService", () => {
  return {
    ...jest.requireActual("../../../src/api/services/ConnectedAccountsService"),
    clearAccountType: jest.fn(),
    getConnectedAccounts: jest.fn(),
    removeConnectedAccount: jest.fn(),
    getConnectAccountErrors: jest.fn()
  };
});

describe("InterestedCreators", () => {
  const REMOVE_ONLY_ACCOUNT = "disconnect-account-conflicting-action";
  const router = { locale: "en-us", push: jest.fn() };
  const informationProp = { nucleusId: ********** } as Information;
  const initialInterestedCreator = anInitialInterestedCreator();
  const interestedCreatorsProps: InterestedCreatorInformationProps = {
    interestedCreator: { ...informationProp } as unknown as InterestedCreator & Information,
    pages: [],
    accessToken: "abc-23457",
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    FLAG_COUNTRIES_BY_TYPE: false
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const errorHandler = jest.fn();
  mockMatchMedia();
  const analytics = { cancelledCreatorApplication: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      analytics,
      metadataClient: {},
      errorHandler,
      configuration: { FLAG_PER_PROGRAM_PROFILE: false }
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getCountries: jest.fn().mockResolvedValue([aCountry(), aCountry()]),
      getLanguages: jest.fn().mockResolvedValue([]),
      getLocales: jest.fn().mockResolvedValue([])
    });
  });

  it("shows modal with link to the logout page when clicking on the close icon", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Hariraj" })]
    });

    render(<InterestedCreators {...interestedCreatorsProps} />);

    await userEvent.click(await screen.findByRole("button", { name: /closeHeader/i }));

    // Verify elements in modal are shown
    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("shows the loader while fetching the connected accounts", async () => {
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue({
      data: [aConnectedAccount({ type: "YOUTUBE", isExpired: false, username: "Hariraj" })]
    });
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: steps,
        isLoading: true
      }
    });

    render(<InterestedCreators {...interestedCreatorsProps} />);

    await waitFor(() => {
      expect(ConnectedAccountsService.getConnectedAccounts).toHaveBeenCalledTimes(1);
      expect(screen.getByAltText("Loading...")).toBeInTheDocument();
    });
  });

  it("shows modal when cancel  button is clicked", async () => {
    render(<InterestedCreators {...interestedCreatorsProps} />);

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    // Verify elements in modal are shown
    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "no" })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(/common:modalConfirmationTitle/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc1/i)).toBeInTheDocument();
    expect(await screen.findByText(/common:confirmationDesc2/i)).toBeInTheDocument();
  });

  it("closes modal when 'no'  button is clicked on modal", async () => {
    render(<InterestedCreators {...interestedCreatorsProps} />);
    // Click Cancel button
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    // Click 'No' on modal
    await userEvent.click(await screen.findByRole("button", { name: "no" }));

    // Verify the modal was closed
    expect(screen.queryByRole("button", { name: /Close$/i })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "no" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: /yes/i })).not.toBeInTheDocument();
    expect(screen.queryByText(/common:modalConfirmationTitle/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc1/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/common:confirmationDesc2/i)).not.toBeInTheDocument();
  });

  it("redirects to the log-out page, and logs event when 'yes' is clicked on modal", async () => {
    render(<InterestedCreators {...interestedCreatorsProps} />);
    // Click on Cancel button
    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    await userEvent.click(await screen.findByRole("button", { name: /yes/i })); // take yes on modal to logout

    // Expect the page to have been redirected to the logout page
    expect(router.push).toHaveBeenCalledTimes(1);
    expect(router.push).toHaveBeenCalledWith("/api/logout");
    expect(analytics.cancelledCreatorApplication).toHaveBeenCalledTimes(1);
    expect(analytics.cancelledCreatorApplication).toHaveBeenCalledWith({ locale: "en-us", page: "/" });
  });

  // Removed next two test cases from InterestedCreatorsInformation.spec and added here.
  // As error toast is not getting dispatched if we mock errorHandling from utils.
  it("prevents removing a connected account, if it's the only one", async () => {
    const dispatch = jest.fn();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch,
      state: {
        onboardingSteps: steps,
        domainError: {
          response: {
            data: {
              code: REMOVE_ONLY_ACCOUNT
            }
          }
        }
      }
    });

    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockResolvedValueOnce(null);

    const accountId = "a1LDF00000K4z0i2AB";
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: accountId, type: "FACEBOOK", isExpired: false, username: "Test User" })
    ]);
    const errorResponse = {
      message:
        "Cannot disconnect channel because: Deleting channel not allowed. Creator should have at least one channel.",
      code: REMOVE_ONLY_ACCOUNT
    };
    (ConnectedAccountsService.removeConnectedAccount as jest.Mock).mockRejectedValue({
      response: {
        data: errorResponse
      }
    });

    renderPage(<InterestedCreators {...interestedCreatorsProps} />);

    await userEvent.click(await screen.findByText("connect-accounts:removeAccount"));

    await userEvent.click(await screen.findByRole("button", { name: /^remove$/i }));

    expect(await screen.findByRole("alert")).toBeInTheDocument();
    expect(await screen.findByText(/unhandledError/)).toBeInTheDocument();
    // Must have at least one account message
    expect(await screen.findByText(/connect-accounts:messages:removeAccountDescription/)).toBeInTheDocument();
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledTimes(1);
    expect(ConnectedAccountsService.removeConnectedAccount).toHaveBeenCalledWith(accountId);
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch,
      state: {
        onboardingSteps: steps,
        domainError: {
          response: {
            data: {
              code: REMOVE_ONLY_ACCOUNT
            }
          }
        }
      }
    });
    (ConnectedAccountsService.clearAccountType as jest.Mock).mockImplementation(() => Promise.resolve());
    (ConnectedAccountsService.getConnectAccountErrors as jest.Mock).mockImplementationOnce(() => Promise.resolve());
    const accountId = "a1LDF00000K4z0i2AB";
    (ConnectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      aConnectedAccount({ id: accountId, type: "FACEBOOK", isExpired: false, username: "Test User" })
    ]);
    const errorResponse = {
      message:
        "Cannot disconnect channel because: Deleting channel not allowed. Creator should have at least one channel.",
      code: REMOVE_ONLY_ACCOUNT
    };
    (ConnectedAccountsService.removeConnectedAccount as jest.Mock).mockRejectedValue({
      response: {
        data: errorResponse
      }
    });

    const { unmount } = renderPage(<InterestedCreators {...interestedCreatorsProps} />);

    await user.click(await screen.findByText("connect-accounts:removeAccount"));

    await user.click(await screen.findByRole("button", { name: /^remove$/i }));

    expect(await screen.findByRole("alert")).toBeInTheDocument();
    expect(await screen.findByText(/unhandledError/)).toBeInTheDocument();
    // Must have at least one account message
    expect(await screen.findByText(/connect-accounts:messages:removeAccountDescription/)).toBeInTheDocument();

    await user.click(await screen.findByRole("button", { name: /Close$/i }));

    triggerAnimationEnd(await screen.findByText(/unhandledError/));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith("DOMAIN_ERROR", dispatch);
    });
    unmount();
    jest.useRealTimers();
  });
});

@import "../migration/information.css";
@import "../migration/franchises-you-play.css";
@import "../migration/communication-preferences.css";
@import "../migration/login-error.css";
@import "../migration/connect-accounts.css";
@import "../migration/terms-and-conditions.css";

.mg-container {
  @apply absolute z-0 flex min-h-full w-full flex-col items-center justify-center overflow-y-hidden bg-migration-default sm:px-meas8 md:py-meas8 xl:bg-migration-web;
}
.mg-container.mg-loading {
  @apply h-full;
}
.mg-bg {
  @apply absolute top-[180px] hidden h-[1400px] w-full max-w-[1440px] xl:block xl:bg-migration-shape xl:bg-contain xl:bg-center xl:bg-no-repeat;
  z-index: -1;
}
.mg-page {
  @apply flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}
.link {
  @apply cursor-pointer;
}
.mg-header-container {
  @apply flex w-full flex-col pt-meas7 md:mb-meas24;
}
.mg-header {
  @apply flex w-full flex-row pb-meas20 text-gray-10;
}
.mg-header-back {
  @apply block flex-1 justify-start xl:hidden;
}
.mg-header-logo {
  @apply hidden justify-between font-display-regular font-bold xs:text-mobile-h5 md:block md:text-tablet-h5 lg:text-desktop-h5 xl:flex-1 xl:justify-start;
}
.mg-header-logo > a > span {
  @apply ml-meas4;
}
.mg-header-close {
  @apply mr-meas7 flex flex-1 justify-end md:mr-[51px] md:mt-[19px] xl:mr-meas8 xl:mt-[17px];
}
.mg-header-logo .icon-block {
  @apply m-meas4 inline;
}
.mg-header-logo .ea-logo {
  @apply inline h-meas20 w-meas20 fill-gray-10;
}
.mg-intro {
  @apply m-meas8 text-center md:w-[630px];
}
.mg-intro-title {
  @apply pb-meas10 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}
.mg-intro-description {
  @apply font-text-regular  xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.mg-intro-description[data-disabled="true"] {
  @apply opacity-50;
}
/* Form container*/
.mg-page form {
  @apply flex w-11/12 flex-col items-center justify-center md:w-[640px] xl:w-[672px];
}
/* Migration footer */
.mg-footer-container {
  @apply mt-meas16 w-full border-t border-white border-opacity-[0.33] py-meas16 pl-meas8 text-right xl:w-[1060px];
}
.mg-footer-container .btn-tertiary {
  @apply mr-meas8;
}

.mg-footer-container svg.icon.icon-hide {
  @apply hidden;
}

/* Creator type */
.creator-type {
  @apply h-meas35 xl:h-meas40;
}
.creator-types-container {
  @apply flex flex-col items-center;
}

.creator-types-container > div.mg-intro:first-child {
  @apply w-[285px] md:w-[682px] xl:w-[894px];
}
.creator-types-container > form {
  @apply w-11/12 pt-[27px] md:w-[640px] xl:w-[1032px];
}
.creator-types-container > form .checkmark {
  @apply items-end;
}
.creator-types-container .card-col .image-as-icon {
  @apply h-[122px] px-meas2 pt-meas16 xl:h-[14rem] xl:px-meas4 xl:pt-meas34;
}
.input-box-label {
  @apply font-text-regular;
}
.mg-header-close .icon-block {
  @apply cursor-pointer;
}
.mg-header-close .icon-block svg {
  @apply md:h-meas9 md:w-meas9;
}
.stepper-back {
  @apply hidden xl:block;
}

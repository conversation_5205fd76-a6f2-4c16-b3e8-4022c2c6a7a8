import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationPendingPagePropsController from "./controllers/ApplicationPendingPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";

const applicationPendingPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationPendingPagePropsController(
      ApiContainer.get("options"),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationPendingPageProps;

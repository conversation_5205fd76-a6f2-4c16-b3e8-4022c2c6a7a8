import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationRejectedPagePropsController from "./controllers/ApplicationRejectedPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";

const applicationRejectedPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationRejectedPagePropsController(
      ApiContainer.get("options"),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationRejectedPageProps;

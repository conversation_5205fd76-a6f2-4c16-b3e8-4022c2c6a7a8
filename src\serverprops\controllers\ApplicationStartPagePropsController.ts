import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { StartProps } from "pages/interested-creators/start";
import featureFlags from "utils/feature-flags";

export default class ApplicationStartPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<StartProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<StartProps>> {
    if (!featureFlags.isInterestedCreatorFlowEnabled()) {
      return { notFound: true };
    }

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "interested-creator-start-page",
          "index",
          "information"
        ]))
      }
    };
  }
}

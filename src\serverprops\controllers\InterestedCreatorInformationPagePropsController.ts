import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { FacebookPages } from "@src/accounts/ConnectedAccountsHttpClient";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import {
  Information,
  InterestedCreatorInformationProps,
  interestedCreatorPages
} from "pages/interested-creators/information";
import featureFlags from "utils/feature-flags";
import crypto from "crypto";

export default class InterestedCreatorInformationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<InterestedCreatorInformationProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient,
    private readonly page?: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<InterestedCreatorInformationProps>> {
    const identity = this.identity(req);
    const nucleusId = identity.nucleusId;
    const program = identity.programs[0].code;
    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      defaultGamerTag: identity.username,
      dateOfBirth: identity.dateOfBirth,
      analyticsId: crypto.createHash("sha256").update(nucleusId.toString()).digest("base64")
    };

    const interestedCreatorFromSession = this.hasSession(req, `${program}.interestedCreator`)
      ? this.session(req, `${program}.interestedCreator`) === true
        ? (defaultInterestedCreator as unknown as InterestedCreator & Information)
        : (this.session(req, `${program}.interestedCreator`) as InterestedCreator & Information)
      : null;

    const interestedCreator = await this.getInterestedCreator(nucleusId, interestedCreatorFromSession);
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };

    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        pages,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "information",
          "communication-preferences",
          "opportunities",
          "connect-accounts",
          "add-content"
        ])),
        FLAG_COUNTRIES_BY_TYPE: featureFlags.isCountriesByTypeEnabled()
      }
    };
  }

  private async getInterestedCreator(
    nucleusId: number,
    interestedCreator: InterestedCreator & Information
  ): Promise<(InterestedCreator & Information) | null> {
    if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      return interestedCreator;
    }

    let existingApplicantInformation;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplicantInformation = await this.applications.forCreatorWithProgram(nucleusId, config.PROGRAM_CODE);
    } else {
      existingApplicantInformation = await this.applications.forCreatorWithApplicationStatus(nucleusId);
    }
    if (!existingApplicantInformation) {
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.information) {
      interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.creatorTypes) {
      if (
        (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
        existingApplicantInformation
      ) {
        interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation?.creatorTypes;
      }
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.franchises) {
      interestedCreator = {
        ...interestedCreator,
        preferredFranchises: [
          ...interestedCreator.preferredFranchises,
          ...(existingApplicantInformation.applicantInformation?.preferredFranchises ?? [])
        ]
      };
      return interestedCreator;
    }

    interestedCreator = {
      ...interestedCreator,
      createdDate: existingApplicantInformation.createdDateformattedWithoutTime(this.currentLocale)
    };

    return interestedCreator;
  }
}
